#!/bin/bash

# Ray Cluster Head Node Startup Script
# Distributed Timbre Transfer with Ray Cluster
#
# Features:
# - Ray head node with dashboard
# - 6 replicas on local GPUs (expandable to 12 with worker)
# - Mixed Precision (FP16) for 82% faster inference
# - Reduced flow steps (20) for optimal speed/quality balance
# - 18 concurrent request capacity (expandable to 36)
# - Ready for distributed worker connections

echo "� Starting Ray Cluster Head Node for Distributed Timbre Transfer..."
echo "📊 Configuration: 6 replicas (local), Mixed Precision, 20 flow steps"
echo "🚀 Current capacity: 18 concurrent requests"
echo "🔗 Ready for worker connections to expand to 36 concurrent requests"

cd /root/ai_compute/Timbre-Transfer

# Activate virtual environment
if [ -f ".venv/bin/activate" ]; then
    echo "🐍 Activating virtual environment..."
    source .venv/bin/activate
fi

# Set GPU visibility (GPUs 1,2,3 - leaving GPU 0 free)
export CUDA_VISIBLE_DEVICES=1,2,3

# Start Ray head node with distributed cluster support
echo "🚀 Starting Ray head node..."
python3 start_ray_head.py \
    --head-ip ************* \
    --port 10001 \
    --dashboard-port 8265 \
    --num-gpus 3

echo "✅ Ray cluster head node startup completed"


