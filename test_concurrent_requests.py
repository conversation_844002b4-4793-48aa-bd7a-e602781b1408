#!/usr/bin/env python3
"""
Comprehensive concurrent request test for Ray Serve Timbre Transfer
Tests actual concurrent capacity across all GPUs and replicas
"""

import asyncio
import aiohttp
import time
import json
import numpy as np
import soundfile as sf
from pathlib import Path
import tempfile
import os
import logging
from concurrent.futures import ThreadPoolExecutor
import threading

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConcurrentTester:
    def __init__(self, base_url="http://localhost:8011"):
        self.base_url = base_url
        self.results = []
        self.lock = threading.Lock()
        
    def create_test_audio(self, duration=2.0, sample_rate=24000):
        """Create test audio file"""
        # Generate a simple sine wave
        t = np.linspace(0, duration, int(sample_rate * duration))
        frequency = 440  # A4 note
        audio = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # Add some noise to make it more realistic
        noise = 0.05 * np.random.randn(len(audio))
        audio = audio + noise
        
        return audio.astype(np.float32)
    
    async def send_request(self, session, request_id, source_audio_path, reference_audio_path):
        """Send a single timbre transfer request"""
        start_time = time.time()

        try:
            # Prepare form data
            data = aiohttp.FormData()

            # Read audio files into memory first
            with open(source_audio_path, 'rb') as f:
                source_data = f.read()

            with open(reference_audio_path, 'rb') as f:
                reference_data = f.read()

            # Add audio files to form data
            data.add_field('source_audio', source_data, filename='source.wav', content_type='audio/wav')
            data.add_field('reference_audio', reference_data, filename='reference.wav', content_type='audio/wav')
            
            # Add parameters
            data.add_field('noise_cancellation', 'false')
            data.add_field('normalize_audio_flag', 'false')
            data.add_field('flow_matching_steps', '10')  # Reduced for faster testing
            
            # Send request
            logger.info(f"🚀 Request {request_id}: Starting...")
            async with session.post(f"{self.base_url}/timbre-transfer/", data=data) as response:
                end_time = time.time()
                latency = end_time - start_time
                
                if response.status == 200:
                    # Get response headers
                    inference_latency = response.headers.get('X-Inference-Latency', 'N/A')
                    
                    result = {
                        'request_id': request_id,
                        'status': 'success',
                        'total_latency': latency,
                        'inference_latency': float(inference_latency) if inference_latency != 'N/A' else None,
                        'start_time': start_time,
                        'end_time': end_time
                    }
                    logger.info(f"✅ Request {request_id}: SUCCESS in {latency:.2f}s (inference: {inference_latency}s)")
                else:
                    error_text = await response.text()
                    result = {
                        'request_id': request_id,
                        'status': 'error',
                        'total_latency': latency,
                        'error': f"HTTP {response.status}: {error_text}",
                        'start_time': start_time,
                        'end_time': end_time
                    }
                    logger.error(f"❌ Request {request_id}: ERROR {response.status} in {latency:.2f}s")
                
        except Exception as e:
            end_time = time.time()
            latency = end_time - start_time
            result = {
                'request_id': request_id,
                'status': 'exception',
                'total_latency': latency,
                'error': str(e),
                'start_time': start_time,
                'end_time': end_time
            }
            logger.error(f"💥 Request {request_id}: EXCEPTION in {latency:.2f}s - {e}")
        
        with self.lock:
            self.results.append(result)
        
        return result
    
    async def test_concurrent_capacity(self, num_requests=15, batch_size=5):
        """Test concurrent request handling capacity"""
        logger.info(f"🧪 Testing concurrent capacity with {num_requests} requests in batches of {batch_size}")
        
        # Create test audio files
        logger.info("🎵 Creating test audio files...")
        source_audio = self.create_test_audio(duration=3.0)  # 3 second audio
        reference_audio = self.create_test_audio(duration=2.0)  # 2 second audio
        
        # Save to temporary files
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as src_file, \
             tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as ref_file:
            
            sf.write(src_file.name, source_audio, 24000)
            sf.write(ref_file.name, reference_audio, 24000)
            
            source_path = src_file.name
            reference_path = ref_file.name
        
        try:
            # Test in batches to see how many concurrent requests can be handled
            connector = aiohttp.TCPConnector(limit=50, limit_per_host=50)
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minute timeout
            
            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                
                # Send requests in batches
                for batch_start in range(0, num_requests, batch_size):
                    batch_end = min(batch_start + batch_size, num_requests)
                    batch_requests = list(range(batch_start, batch_end))
                    
                    logger.info(f"📦 Sending batch {batch_start//batch_size + 1}: requests {batch_start+1}-{batch_end}")
                    
                    # Send batch concurrently
                    batch_start_time = time.time()
                    tasks = [
                        self.send_request(session, req_id, source_path, reference_path)
                        for req_id in batch_requests
                    ]
                    
                    # Wait for batch to complete
                    await asyncio.gather(*tasks)
                    batch_end_time = time.time()
                    
                    logger.info(f"📦 Batch {batch_start//batch_size + 1} completed in {batch_end_time - batch_start_time:.2f}s")
                    
                    # Small delay between batches
                    if batch_end < num_requests:
                        await asyncio.sleep(1)
        
        finally:
            # Clean up temporary files
            try:
                os.unlink(source_path)
                os.unlink(reference_path)
            except:
                pass
    
    def analyze_results(self):
        """Analyze test results and print statistics"""
        if not self.results:
            logger.error("No results to analyze!")
            return
        
        logger.info("\n" + "="*60)
        logger.info("📊 CONCURRENT REQUEST TEST RESULTS")
        logger.info("="*60)
        
        # Basic statistics
        total_requests = len(self.results)
        successful_requests = [r for r in self.results if r['status'] == 'success']
        failed_requests = [r for r in self.results if r['status'] != 'success']
        
        logger.info(f"Total requests: {total_requests}")
        logger.info(f"Successful: {len(successful_requests)} ({len(successful_requests)/total_requests*100:.1f}%)")
        logger.info(f"Failed: {len(failed_requests)} ({len(failed_requests)/total_requests*100:.1f}%)")
        
        if successful_requests:
            # Latency statistics
            total_latencies = [r['total_latency'] for r in successful_requests]
            inference_latencies = [r['inference_latency'] for r in successful_requests if r['inference_latency']]
            
            logger.info(f"\n📈 LATENCY STATISTICS:")
            logger.info(f"Total latency - Min: {min(total_latencies):.2f}s, Max: {max(total_latencies):.2f}s, Avg: {np.mean(total_latencies):.2f}s")
            if inference_latencies:
                logger.info(f"Inference latency - Min: {min(inference_latencies):.2f}s, Max: {max(inference_latencies):.2f}s, Avg: {np.mean(inference_latencies):.2f}s")
            
            # Concurrency analysis
            start_times = [r['start_time'] for r in self.results]
            end_times = [r['end_time'] for r in self.results]
            
            earliest_start = min(start_times)
            latest_end = max(end_times)
            total_test_duration = latest_end - earliest_start
            
            logger.info(f"\n⏱️  CONCURRENCY ANALYSIS:")
            logger.info(f"Total test duration: {total_test_duration:.2f}s")
            logger.info(f"Theoretical sequential time: {sum(total_latencies):.2f}s")
            logger.info(f"Concurrency factor: {sum(total_latencies)/total_test_duration:.2f}x")
            
            # Timeline analysis - check how many requests were running simultaneously
            timeline = []
            for r in self.results:
                timeline.append((r['start_time'], 'start', r['request_id']))
                timeline.append((r['end_time'], 'end', r['request_id']))
            
            timeline.sort()
            
            max_concurrent = 0
            current_concurrent = 0
            concurrent_at_max = []
            
            for timestamp, event_type, req_id in timeline:
                if event_type == 'start':
                    current_concurrent += 1
                    if current_concurrent > max_concurrent:
                        max_concurrent = current_concurrent
                        concurrent_at_max = [req_id]
                    elif current_concurrent == max_concurrent:
                        concurrent_at_max.append(req_id)
                else:  # end
                    current_concurrent -= 1
            
            logger.info(f"Maximum concurrent requests: {max_concurrent}")
            logger.info(f"Expected concurrent capacity: 11 replicas × 3 requests = 33 concurrent")
            logger.info(f"Utilization: {max_concurrent/33*100:.1f}% of expected capacity")
        
        # Error analysis
        if failed_requests:
            logger.info(f"\n❌ ERROR ANALYSIS:")
            error_types = {}
            for r in failed_requests:
                error_key = r.get('error', 'Unknown error')[:50]  # First 50 chars
                error_types[error_key] = error_types.get(error_key, 0) + 1
            
            for error, count in error_types.items():
                logger.info(f"  {error}: {count} occurrences")
        
        logger.info("="*60)

async def main():
    """Main test function"""
    tester = ConcurrentTester()
    
    # Test with increasing number of concurrent requests
    logger.info("🚀 Starting comprehensive concurrent request test...")
    
    # First, test with a moderate number of requests
    await tester.test_concurrent_capacity(num_requests=15, batch_size=5)
    
    # Analyze results
    tester.analyze_results()

if __name__ == "__main__":
    asyncio.run(main())
