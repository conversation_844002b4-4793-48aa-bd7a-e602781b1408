#!/usr/bin/env python3
"""
Progressive Load Test for Ray Serve Timbre Transfer
Tests increasing concurrent loads to find optimal capacity and identify bottlenecks
"""

import asyncio
import aiohttp
import time
import numpy as np
import soundfile as sf
import tempfile
import os
import logging
from concurrent.futures import ThreadPoolExecutor
import threading

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProgressiveLoadTester:
    def __init__(self, base_url="http://localhost:8011"):
        self.base_url = base_url
        self.results = []
        self.lock = threading.Lock()
        
    def create_test_audio(self, duration=1.0, sample_rate=24000):
        """Create simple test audio"""
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio = 0.3 * np.sin(2 * np.pi * 440 * t)  # Simple sine wave
        return audio.astype(np.float32)
    
    async def send_single_request(self, session, request_id, test_name):
        """Send a single request with embedded audio data"""
        start_time = time.time()
        
        try:
            # Create audio data
            audio_data = self.create_test_audio(duration=1.0)  # 1 second audio for speed
            
            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as src_file, \
                 tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as ref_file:
                
                sf.write(src_file.name, audio_data, 24000)
                sf.write(ref_file.name, audio_data, 24000)
                
                src_path = src_file.name
                ref_path = ref_file.name
            
            # Read files into memory
            with open(src_path, 'rb') as f:
                src_data = f.read()
            with open(ref_path, 'rb') as f:
                ref_data = f.read()
            
            # Clean up temp files immediately
            os.unlink(src_path)
            os.unlink(ref_path)
            
            # Prepare form data
            data = aiohttp.FormData()
            data.add_field('source_audio', src_data, filename=f'source_{request_id}.wav', content_type='audio/wav')
            data.add_field('reference_audio', ref_data, filename=f'reference_{request_id}.wav', content_type='audio/wav')
            data.add_field('noise_cancellation', 'false')
            data.add_field('normalize_audio_flag', 'false')
            data.add_field('flow_matching_steps', '8')  # Very fast for testing
            
            logger.info(f"🚀 {test_name} - Request {request_id}: Starting...")
            
            # Send request
            async with session.post(f"{self.base_url}/timbre-transfer/", data=data) as response:
                end_time = time.time()
                latency = end_time - start_time
                
                if response.status == 200:
                    # Read response to complete the request
                    content = await response.read()
                    inference_latency = response.headers.get('X-Inference-Latency', 'N/A')
                    
                    result = {
                        'test_name': test_name,
                        'request_id': request_id,
                        'status': 'success',
                        'total_latency': latency,
                        'inference_latency': float(inference_latency) if inference_latency != 'N/A' else None,
                        'start_time': start_time,
                        'end_time': end_time,
                        'response_size': len(content)
                    }
                    logger.info(f"✅ {test_name} - Request {request_id}: SUCCESS in {latency:.2f}s")
                else:
                    error_text = await response.text()
                    result = {
                        'test_name': test_name,
                        'request_id': request_id,
                        'status': 'error',
                        'total_latency': latency,
                        'error': f"HTTP {response.status}: {error_text[:100]}",
                        'start_time': start_time,
                        'end_time': end_time
                    }
                    logger.error(f"❌ {test_name} - Request {request_id}: ERROR {response.status}")
                
        except Exception as e:
            end_time = time.time()
            latency = end_time - start_time
            result = {
                'test_name': test_name,
                'request_id': request_id,
                'status': 'exception',
                'total_latency': latency,
                'error': str(e)[:100],
                'start_time': start_time,
                'end_time': end_time
            }
            logger.error(f"💥 {test_name} - Request {request_id}: EXCEPTION - {str(e)[:50]}")
        
        with self.lock:
            self.results.append(result)
        
        return result
    
    async def test_load_level(self, num_requests, test_name):
        """Test a specific load level"""
        logger.info(f"\n🧪 {test_name}: Testing {num_requests} concurrent requests...")
        
        # Setup session with appropriate limits
        connector = aiohttp.TCPConnector(limit=150, limit_per_host=100)
        timeout = aiohttp.ClientTimeout(total=300)  # 5 minute timeout
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Send all requests concurrently
            start_time = time.time()
            tasks = [
                self.send_single_request(session, i, test_name)
                for i in range(num_requests)
            ]
            
            # Wait for all requests to complete
            await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_test_time = end_time - start_time
            logger.info(f"📊 {test_name}: All {num_requests} requests completed in {total_test_time:.2f}s")
            
            return total_test_time
    
    async def progressive_load_test(self):
        """Run progressive load test with increasing concurrent requests"""
        logger.info("🚀 Starting Progressive Load Test...")
        logger.info("📊 Testing increasing concurrent loads to find optimal capacity")
        
        # Test different load levels
        load_levels = [
            (10, "Baseline Load"),
            (20, "Light Load"), 
            (36, "Expected Capacity"),
            (50, "Medium Load"),
            (75, "Heavy Load"),
            (100, "Stress Test"),
            (120, "Maximum Test")
        ]
        
        test_results = {}
        
        for num_requests, test_name in load_levels:
            try:
                # Clear previous results for this test
                initial_count = len(self.results)
                
                # Run the test
                test_time = await self.test_load_level(num_requests, test_name)
                
                # Analyze results for this test level
                test_results[num_requests] = self.analyze_test_level(test_name, initial_count)
                
                # Wait between tests to let system recover
                logger.info(f"⏳ Waiting 10 seconds before next test...")
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"❌ {test_name} failed: {e}")
                test_results[num_requests] = {'error': str(e)}
        
        # Final comprehensive analysis
        self.analyze_progressive_results(test_results)
    
    def analyze_test_level(self, test_name, start_index):
        """Analyze results for a specific test level"""
        test_results = [r for r in self.results[start_index:] if r.get('test_name') == test_name]
        
        if not test_results:
            return {'error': 'No results'}
        
        successful = [r for r in test_results if r['status'] == 'success']
        failed = [r for r in test_results if r['status'] != 'success']
        
        success_rate = len(successful) / len(test_results) * 100
        
        analysis = {
            'total_requests': len(test_results),
            'successful': len(successful),
            'failed': len(failed),
            'success_rate': success_rate,
        }
        
        if successful:
            latencies = [r['total_latency'] for r in successful]
            analysis.update({
                'avg_latency': np.mean(latencies),
                'min_latency': min(latencies),
                'max_latency': max(latencies),
                'p95_latency': np.percentile(latencies, 95)
            })
        
        logger.info(f"📈 {test_name} Results: {len(successful)}/{len(test_results)} success ({success_rate:.1f}%)")
        if successful:
            logger.info(f"   Latency: avg={analysis['avg_latency']:.2f}s, p95={analysis['p95_latency']:.2f}s")
        
        return analysis
    
    def analyze_progressive_results(self, test_results):
        """Analyze overall progressive test results"""
        logger.info("\n" + "="*80)
        logger.info("📊 PROGRESSIVE LOAD TEST RESULTS")
        logger.info("="*80)
        
        logger.info(f"{'Load Level':<15} {'Requests':<10} {'Success Rate':<12} {'Avg Latency':<12} {'P95 Latency':<12}")
        logger.info("-" * 80)
        
        optimal_load = None
        max_successful_load = 0
        
        for load_level, results in test_results.items():
            if 'error' in results:
                logger.info(f"{load_level:<15} {'ERROR':<10} {'-':<12} {'-':<12} {'-':<12}")
                continue
                
            success_rate = results['success_rate']
            avg_lat = results.get('avg_latency', 0)
            p95_lat = results.get('p95_latency', 0)
            
            logger.info(f"{load_level:<15} {results['successful']:<10} {success_rate:<11.1f}% {avg_lat:<11.2f}s {p95_lat:<11.2f}s")
            
            # Find optimal load (>95% success rate with reasonable latency)
            if success_rate >= 95 and avg_lat < 30:  # 30s reasonable for audio processing
                optimal_load = load_level
                max_successful_load = max(max_successful_load, results['successful'])
        
        logger.info("-" * 80)
        if optimal_load:
            logger.info(f"🎯 OPTIMAL LOAD LEVEL: {optimal_load} concurrent requests")
            logger.info(f"🚀 MAXIMUM SUCCESSFUL CAPACITY: {max_successful_load} requests")
        else:
            logger.info("⚠️  No optimal load level found - system may be overloaded")
        
        logger.info("="*80)

async def main():
    """Main test function"""
    tester = ProgressiveLoadTester()
    
    # Run progressive load test
    await tester.progressive_load_test()

if __name__ == "__main__":
    asyncio.run(main())
