#!/usr/bin/env python3
"""
Simple concurrent test to check Ray Serve deployment capacity
"""

import asyncio
import aiohttp
import time
import numpy as np
import soundfile as sf
import tempfile
import os
import logging
from concurrent.futures import ThreadPoolExecutor
import threading

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleConcurrentTester:
    def __init__(self, base_url="http://localhost:8011"):
        self.base_url = base_url
        self.results = []
        self.lock = threading.Lock()
        
    def create_test_audio(self, duration=1.0, sample_rate=24000):
        """Create simple test audio"""
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio = 0.3 * np.sin(2 * np.pi * 440 * t)  # Simple sine wave
        return audio.astype(np.float32)
    
    async def send_single_request(self, session, request_id):
        """Send a single request with embedded audio data"""
        start_time = time.time()
        
        try:
            # Create audio data
            audio_data = self.create_test_audio(duration=1.0)  # 1 second audio for speed
            
            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as src_file, \
                 tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as ref_file:
                
                sf.write(src_file.name, audio_data, 24000)
                sf.write(ref_file.name, audio_data, 24000)
                
                src_path = src_file.name
                ref_path = ref_file.name
            
            # Read files into memory
            with open(src_path, 'rb') as f:
                src_data = f.read()
            with open(ref_path, 'rb') as f:
                ref_data = f.read()
            
            # Clean up temp files immediately
            os.unlink(src_path)
            os.unlink(ref_path)
            
            # Prepare form data
            data = aiohttp.FormData()
            data.add_field('source_audio', src_data, filename=f'source_{request_id}.wav', content_type='audio/wav')
            data.add_field('reference_audio', ref_data, filename=f'reference_{request_id}.wav', content_type='audio/wav')
            data.add_field('noise_cancellation', 'false')
            data.add_field('normalize_audio_flag', 'false')
            data.add_field('flow_matching_steps', '8')  # Very fast for testing
            
            logger.info(f"🚀 Request {request_id}: Starting...")
            
            # Send request
            async with session.post(f"{self.base_url}/timbre-transfer/", data=data) as response:
                end_time = time.time()
                latency = end_time - start_time
                
                if response.status == 200:
                    # Read response to complete the request
                    content = await response.read()
                    inference_latency = response.headers.get('X-Inference-Latency', 'N/A')
                    
                    result = {
                        'request_id': request_id,
                        'status': 'success',
                        'total_latency': latency,
                        'inference_latency': float(inference_latency) if inference_latency != 'N/A' else None,
                        'start_time': start_time,
                        'end_time': end_time,
                        'response_size': len(content)
                    }
                    logger.info(f"✅ Request {request_id}: SUCCESS in {latency:.2f}s (inference: {inference_latency}s)")
                else:
                    error_text = await response.text()
                    result = {
                        'request_id': request_id,
                        'status': 'error',
                        'total_latency': latency,
                        'error': f"HTTP {response.status}: {error_text[:100]}",
                        'start_time': start_time,
                        'end_time': end_time
                    }
                    logger.error(f"❌ Request {request_id}: ERROR {response.status} in {latency:.2f}s")
                
        except Exception as e:
            end_time = time.time()
            latency = end_time - start_time
            result = {
                'request_id': request_id,
                'status': 'exception',
                'total_latency': latency,
                'error': str(e)[:100],
                'start_time': start_time,
                'end_time': end_time
            }
            logger.error(f"💥 Request {request_id}: EXCEPTION in {latency:.2f}s - {str(e)[:50]}")
        
        with self.lock:
            self.results.append(result)
        
        return result
    
    async def test_concurrent_requests(self, num_requests=20):
        """Test concurrent request handling"""
        logger.info(f"🧪 Testing {num_requests} concurrent requests...")
        
        # Setup session with appropriate limits
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        timeout = aiohttp.ClientTimeout(total=180)  # 3 minute timeout
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Send all requests concurrently
            start_time = time.time()
            tasks = [
                self.send_single_request(session, i)
                for i in range(num_requests)
            ]
            
            # Wait for all requests to complete
            await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            total_test_time = end_time - start_time
            logger.info(f"📊 All {num_requests} requests completed in {total_test_time:.2f}s")
    
    def analyze_results(self):
        """Analyze and print test results"""
        if not self.results:
            logger.error("No results to analyze!")
            return
        
        logger.info("\n" + "="*60)
        logger.info("📊 CONCURRENT REQUEST TEST RESULTS")
        logger.info("="*60)
        
        # Basic statistics
        total_requests = len(self.results)
        successful_requests = [r for r in self.results if r['status'] == 'success']
        failed_requests = [r for r in self.results if r['status'] != 'success']
        
        logger.info(f"Total requests: {total_requests}")
        logger.info(f"Successful: {len(successful_requests)} ({len(successful_requests)/total_requests*100:.1f}%)")
        logger.info(f"Failed: {len(failed_requests)} ({len(failed_requests)/total_requests*100:.1f}%)")
        
        if successful_requests:
            # Timing analysis
            start_times = [r['start_time'] for r in self.results]
            end_times = [r['end_time'] for r in self.results]
            
            earliest_start = min(start_times)
            latest_end = max(end_times)
            total_test_duration = latest_end - earliest_start
            
            # Calculate concurrent requests over time
            timeline = []
            for r in self.results:
                timeline.append((r['start_time'], 'start', r['request_id']))
                timeline.append((r['end_time'], 'end', r['request_id']))
            
            timeline.sort()
            
            max_concurrent = 0
            current_concurrent = 0
            
            for timestamp, event_type, req_id in timeline:
                if event_type == 'start':
                    current_concurrent += 1
                    max_concurrent = max(max_concurrent, current_concurrent)
                else:  # end
                    current_concurrent -= 1
            
            # Latency statistics
            total_latencies = [r['total_latency'] for r in successful_requests]
            inference_latencies = [r['inference_latency'] for r in successful_requests if r['inference_latency']]
            
            logger.info(f"\n📈 PERFORMANCE METRICS:")
            logger.info(f"Total test duration: {total_test_duration:.2f}s")
            logger.info(f"Maximum concurrent requests: {max_concurrent}")
            logger.info(f"Expected capacity: 120 requests (12 replicas × 10 max_ongoing_requests)")
            logger.info(f"Utilization: {max_concurrent/120*100:.1f}% of expected capacity")
            
            if total_latencies:
                logger.info(f"\n⏱️  LATENCY STATISTICS:")
                logger.info(f"Total latency - Min: {min(total_latencies):.2f}s, Max: {max(total_latencies):.2f}s, Avg: {np.mean(total_latencies):.2f}s")
                if inference_latencies:
                    logger.info(f"Inference latency - Min: {min(inference_latencies):.2f}s, Max: {max(inference_latencies):.2f}s, Avg: {np.mean(inference_latencies):.2f}s")
        
        # Error analysis
        if failed_requests:
            logger.info(f"\n❌ ERROR ANALYSIS:")
            error_types = {}
            for r in failed_requests:
                error_key = r.get('error', 'Unknown error')[:50]
                error_types[error_key] = error_types.get(error_key, 0) + 1
            
            for error, count in error_types.items():
                logger.info(f"  {error}: {count} occurrences")
        
        logger.info("="*60)

async def main():
    """Main test function"""
    tester = SimpleConcurrentTester()
    
    # Test with different numbers of concurrent requests
    logger.info("🚀 Starting simple concurrent request test...")

    # Test with 100 concurrent requests to stress test all GPUs
    await tester.test_concurrent_requests(num_requests=100)
    
    # Analyze results
    tester.analyze_results()

if __name__ == "__main__":
    asyncio.run(main())
