# Ray Serve Configuration for Timbre Transfer Only
# Optimized for GPU acceleration and performance

# Server configuration
server:
  host: "0.0.0.0"
  port: 8010
  workers: 1

# Ray configuration
ray:
  # GPU allocation - using GPUs 0, 2, 3 (skipping GPU 1)
  num_gpus: 3  # Total GPUs for ttoimbre transfer
  num_cpus: 12  # Total CPUs  use (4 per GPU)
  object_store_memory: 4000000000  # 4GB object store

# Timbre transfer deployment configuration (OPTIMIZED)
timbre_deployment:
  # Resource allocation per replica (optimized for efficient GPU memory usage)
  num_replicas: 6  # Number of model replicas (2 per GPU: 0, 2, 3)
  num_gpus_per_replica: 0.5  # Half GPU allocation per replica (8GB effective)
  num_cpus_per_replica: 3  # CPU cores per replica
  memory_per_replica: 3000000000  # 3GB RAM per replica
  gpu_memory_per_replica: **********  # 8GB GPU memory per replica (0.5 × 16GB)

  # Performance settings (optimized for 2 replicas per GPU)
  max_ongoing_requests: 10  # Max concurrent requests per replica (increased for 100+ concurrent capacity)

  # Optimization settings
  mixed_precision: true  # Enable FP16/TF32 for faster inference
  default_flow_steps: 20  # Reduced from 32 for faster processing
  warmup_enabled: true  # Enable model warmup on startup
  
  # Model optimization (for 16GB GPU memory)
  optimization:
    optimize_for_inference: true  # Inference optimizations
    gpu_memory_fraction: 0.95  # Use 95% of 16GB GPU memory (15.2GB)
    enable_memory_growth: true  # Allow dynamic memory allocation
    cache_models_in_gpu: true  # Keep models in GPU memory
    
  # Default inference parameters
  default_params:
    flow_matching_steps: 32  # Default flow matching steps
    noise_cancellation: True  # Default noise cancellation
    normalize_audio: True  # Default audio normalization

  # Multi-GPU configuration (OPTIMIZED for 2 replicas per GPU)
  gpu_setup:
    total_gpus: 4  # Total GPUs in system    enable_mixed_precision: false  # Keep FP32 for quality

    used_gpus: [1, 2, 3]  # GPUs used for timbre transfer (16GB each)    replicas_per_gpu: 2  # 2 replicas per physical GPU
    gpu_memory_per_device: 16000000000  # 16GB per GPU
    gpu_memory_per_replica: **********  # 8GB per replica (0.5 × 16GB)
    total_gpu_memory: 4**********  # 48GB total (3 × 16GB)
    concurrent_capacity: 120  # Total concurrent requests (12 replicas × 10 requests)
    optimization_level: "high"  # Mixed precision + reduced flow steps
    memory_optimization: "efficient_sharing"  # Efficient GPU memory sharing

# Health monitoring
monitoring:
  health_check_period_s: 30  # Health check interval
  health_check_timeout_s: 10  # Health check timeout
  
  # Metrics collection
  metrics_enabled: true
  metrics_export_interval_s: 10

# Performance tuning
performance:
  # Autoscaling (experimental)
  autoscaling:
    enabled: false  # Disable for now
    min_replicas: 1
    max_replicas: 4
    target_requests_per_replica: 2
  
  # Batching (not recommended for real-time audio)
  batching:
    enabled: false
    max_batch_size: 1
    batch_wait_timeout_s: 0.1

# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Ray-specific logging
  ray_log_to_driver: true
  ray_log_level: "INFO"

# Security settings
security:
  # CORS configuration
  cors:
    enabled: true
    allow_origins: ["*"]  # Restrict in production
    allow_methods: ["GET", "POST"]
    allow_headers: ["*"]
  
  # Request limits
  limits:
    max_file_size_mb: 100  # Max audio file size
    request_timeout_s: 300  # 5 minutes timeout

# Development settings
development:
  debug: false
  hot_reload: false
  profiling_enabled: false

# Production settings (for deployment)
production:
  # High availability
  replica_failure_tolerance: 1
  graceful_shutdown_timeout_s: 30
  
  # Load balancing
  load_balancing_strategy: "round_robin"  # or "least_connections"
  
  # Resource monitoring
  resource_monitoring:
    enabled: true
    alert_on_high_gpu_usage: 90  # Alert when GPU usage > 90%
    alert_on_high_memory_usage: 85  # Alert when memory usage > 85%
